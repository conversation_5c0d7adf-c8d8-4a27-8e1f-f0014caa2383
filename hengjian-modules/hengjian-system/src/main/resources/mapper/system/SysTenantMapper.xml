<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengjian.system.mapper.SysTenantMapper">

    <select id="queryPage" resultType="com.hengjian.system.domain.vo.SysTenantVo">
        SELECT st.*
        FROM sys_tenant st
        WHERE st.del_flag = '0'
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.tenantId)">
            AND st.tenant_id = #{bo.tenantId}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.tenantType)">
            AND st.tenant_type = #{bo.tenantType}
        </if>
        AND EXISTS (select 1
        from sys_user u
        INNER JOIN sys_user_role ssur on ssur.user_id = u.user_id
        INNER JOIN sys_role ssr ON ssr.role_id = ssur.role_id
        WHERE u.del_flag = '0'
        AND u.tenant_id = st.tenant_id
        AND ssr.status = '0'
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.nickName)">
            AND u.nick_name LIKE CONCAT('%', #{bo.nickName}, '%') AND ssr.role_key = 'admin'
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.contactEmail)">
            AND u.email = #{bo.contactEmail} AND ssr.role_key = 'admin'
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.contactPhone)">
            AND u.phonenumber = #{bo.contactPhone} AND ssr.role_key = 'admin'
        </if>
        )
    </select>

    <select id="getApprovedTenant" resultType="com.hengjian.system.domain.vo.ApprovedTenantVo">
        select  st.tenant_id, st.is_calculation
        from
                  sys_tenant as  st
                 inner join tenant_sup_settle_in_review_record as  suu on suu.tenant_id = st.tenant_id
        where  st.del_flag = 0
          and  suu.del_flag=0
          and st.tenant_type='Distributor'
          and suu.review_state = 'Approved'
            <if test="tenantId != null and tenantId != ''">
                and st.tenant_id=#{tenantId}
            </if>
        <if test="isCalculation != null">
            and st.is_calculation=#{isCalculation}
        </if>
        group by st.tenant_id
    </select>

    <update id="updateTenantIsCalculation">
        update sys_tenant set is_calculation=#{isCalculation}
        where 1=1
        <if test="tenantIds != null and tenantIds.size() != 0">
            and tenant_id in
            <foreach collection="tenantIds" item="tenantID" open="(" separator="," close=")">
                #{tenantID}
            </foreach>
        </if>
    </update>

    <select id="getIsApprovedTenant" resultType="java.lang.String">
        select st.tenant_id
        from
        sys_tenant as st
        inner join tenant_sup_settle_in_review_record as suu on suu.tenant_id = st.tenant_id
        where st.del_flag = 0
        and suu.del_flag=0
        and st.tenant_type='Distributor'
        and suu.review_state = 'Approved'
        <if test="tenantId != null and tenantId != ''">
            and st.tenant_id=#{tenantId}
        </if>
        <if test="isCalculation != null">
            and st.is_calculation=#{isCalculation}
        </if>
    </select>
    <select id="getAllApprovedTenant" resultType="java.lang.String">
        select st.tenant_id
        from  sys_tenant st
                  inner join tenant_sup_settle_in_review_record suu on suu.tenant_id = st.tenant_id
        where
            st.del_flag = 0
          and  suu.del_flag = 0
          and st.tenant_type = 'Distributor'
          and suu.review_state = 'Approved'
    </select>
    <select id="queryByTenantIds" resultType="java.util.Map">
        SELECT tenant_id,channel_flag
        FROM sys_tenant
        WHERE tenant_id IN
        <foreach item="tenantId" index="index" collection="tenantIds" open="(" separator="," close=")">
            #{tenantId}
        </foreach>
    </select>
</mapper>


