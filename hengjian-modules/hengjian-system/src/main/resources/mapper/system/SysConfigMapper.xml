<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengjian.system.mapper.SysConfigMapper">

    <select id="getSysConfigByKey" resultType="java.lang.String">
        select sc.config_value
        from sys_config sc
        <if test="key != null">
            <where>
                sc.config_key = #{key}
            </where>
        </if>
    </select>
</mapper>
