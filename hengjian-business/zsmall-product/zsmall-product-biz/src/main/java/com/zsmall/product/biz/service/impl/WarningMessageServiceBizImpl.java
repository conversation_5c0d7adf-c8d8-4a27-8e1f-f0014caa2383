package com.zsmall.product.biz.service.impl;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.product.biz.service.WarningMessageService;
import com.zsmall.product.entity.domain.bo.warningMessage.WarningMessageBo;
import com.zsmall.product.entity.domain.vo.warningMessage.WarningMessageVo;
import com.zsmall.product.entity.iservice.IWarningMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 预警消息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WarningMessageServiceBizImpl implements WarningMessageService {

    private final IWarningMessageService iWarningMessageService;

    /**
     * 查询预警消息
     */
    @Override
    public R<WarningMessageVo> queryById(Long id) {
        WarningMessageVo vo = iWarningMessageService.queryById(id);
        return R.ok(vo);
    }

    /**
     * 查询预警消息列表
     */
    @Override
    public R<TableDataInfo<WarningMessageVo>> queryPageList(WarningMessageBo bo, PageQuery pageQuery) {
        TableDataInfo<WarningMessageVo> result = iWarningMessageService.queryPageList(bo, pageQuery);
        return R.ok(result);
    }

    /**
     * 查询预警消息列表
     */
    @Override
    public R<List<WarningMessageVo>> queryList(WarningMessageBo bo) {
        List<WarningMessageVo> list = iWarningMessageService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 新增预警消息
     */
    @Override
    public R<Void> insertByBo(WarningMessageBo bo) {
        Boolean result = iWarningMessageService.insertByBo(bo);
        return result ? R.ok() : R.fail("新增失败");
    }

    /**
     * 修改预警消息
     */
    @Override
    public R<Void> updateByBo(WarningMessageBo bo) {
        Boolean result = iWarningMessageService.updateByBo(bo);
        return result ? R.ok() : R.fail("修改失败");
    }

    /**
     * 删除预警消息
     */
    @Override
    public R<Void> deleteWithValidByIds(List<Long> ids) {
        Boolean result = iWarningMessageService.deleteWithValidByIds(ids, true);
        return result ? R.ok() : R.fail("删除失败");
    }

    /**
     * 获取未读消息数量
     */
    @Override
    public R<Long> getUnreadCount(String tenantType, Integer businessType) {
        Long count = iWarningMessageService.countUnreadMessages(tenantType, businessType);
        return R.ok(count);
    }

    /**
     * 批量标记消息为已读
     */
    @Override
    public R<Void> batchMarkAsRead(List<Long> ids) {
        Boolean result = iWarningMessageService.batchMarkAsRead(ids);
        return result ? R.ok() : R.fail("标记已读失败");
    }

    /**
     * 获取当前用户的消息列表
     */
    @Override
    public R<List<WarningMessageVo>> getCurrentUserMessages() {
        try {
            TenantType tenantType = LoginHelper.getTenantTypeEnum();
            String tenantTypeStr = tenantType.name();
            List<WarningMessageVo> messages = iWarningMessageService.getMessagesByTenantType(tenantTypeStr);
            return R.ok(messages);
        } catch (Exception e) {
            log.error("获取当前用户消息列表失败", e);
            return R.fail("获取消息列表失败");
        }
    }
}
