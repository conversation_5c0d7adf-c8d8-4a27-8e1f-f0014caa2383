//package com.zsmall.product.biz.service.impl;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.StrUtil;
//import com.hengjian.common.core.constant.GlobalConstants;
//import com.hengjian.common.redis.utils.RedisUtils;
//import com.zsmall.product.biz.service.StockStatusCacheService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import java.time.Duration;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//
///**
// * 库存状态缓存服务实现
// *
// * <AUTHOR>
// * @date 2025-01-28
// */
//@Slf4j
//@Service
//public class StockStatusCacheServiceImpl implements StockStatusCacheService {
//
//    /**
//     * 库存状态缓存Key前缀
//     */
//    private static final String STOCK_STATUS_KEY_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + "stock:warning:status:";
//
//    /**
//     * 缓存过期时间 (7天)
//     */
//    private static final Duration CACHE_EXPIRE_TIME = Duration.ofDays(7);
//
//    /**
//     * 获取库存状态缓存Key
//     */
//    @Override
//    public String getStockStatusKey(String productSkuCode, String warehouseSystemCode) {
//        return STOCK_STATUS_KEY_PREFIX + productSkuCode + ":" + warehouseSystemCode;
//    }
//
//    /**
//     * 设置库存状态
//     */
//    @Override
//    public void setStockStatus(String productSkuCode, String warehouseSystemCode, boolean hasStock) {
//        if (StrUtil.isBlank(productSkuCode) || StrUtil.isBlank(warehouseSystemCode)) {
//            log.warn("设置库存状态参数为空: productSkuCode={}, warehouseSystemCode={}", productSkuCode, warehouseSystemCode);
//            return;
//        }
//
//        try {
//            String key = getStockStatusKey(productSkuCode, warehouseSystemCode);
//            String value = hasStock ? "1" : "0";
//            RedisUtils.setCacheObject(key, value, CACHE_EXPIRE_TIME);
//            log.debug("设置库存状态成功: key={}, hasStock={}", key, hasStock);
//        } catch (Exception e) {
//            log.error("设置库存状态异常: productSkuCode={}, warehouseSystemCode={}, hasStock={}",
//                     productSkuCode, warehouseSystemCode, hasStock, e);
//        }
//    }
//
//    /**
//     * 获取库存状态
//     */
//    @Override
//    public Boolean getStockStatus(String productSkuCode, String warehouseSystemCode) {
//        if (StrUtil.isBlank(productSkuCode) || StrUtil.isBlank(warehouseSystemCode)) {
//            log.warn("获取库存状态参数为空: productSkuCode={}, warehouseSystemCode={}", productSkuCode, warehouseSystemCode);
//            return null;
//        }
//
//        try {
//            String key = getStockStatusKey(productSkuCode, warehouseSystemCode);
//            String value = RedisUtils.getCacheObject(key);
//
//            if (StrUtil.isBlank(value)) {
//                log.debug("库存状态未初始化: key={}", key);
//                return null;
//            }
//
//            boolean hasStock = "1".equals(value);
//            log.debug("获取库存状态成功: key={}, hasStock={}", key, hasStock);
//            return hasStock;
//
//        } catch (Exception e) {
//            log.error("获取库存状态异常: productSkuCode={}, warehouseSystemCode={}",
//                     productSkuCode, warehouseSystemCode, e);
//            return null;
//        }
//    }
//
//    /**
//     * 批量设置库存状态
//     */
//    @Override
//    public void batchSetStockStatus(Map<String, Boolean> stockStatusMap) {
//        if (CollUtil.isEmpty(stockStatusMap)) {
//            log.warn("批量设置库存状态参数为空");
//            return;
//        }
//
//        try {
//            Map<String, Object> cacheMap = new HashMap<>();
//            for (Map.Entry<String, Boolean> entry : stockStatusMap.entrySet()) {
//                String stockKey = entry.getKey();
//                Boolean hasStock = entry.getValue();
//
//                if (StrUtil.isNotBlank(stockKey) && hasStock != null) {
//                    String key = STOCK_STATUS_KEY_PREFIX + stockKey;
//                    String value = hasStock ? "1" : "0";
//                    cacheMap.put(key, value);
//                }
//            }
//
//            if (CollUtil.isNotEmpty(cacheMap)) {
//                RedisUtils.setCacheMap(cacheMap, CACHE_EXPIRE_TIME);
//                log.info("批量设置库存状态成功: count={}", cacheMap.size());
//            }
//
//        } catch (Exception e) {
//            log.error("批量设置库存状态异常: stockStatusMap={}", stockStatusMap, e);
//        }
//    }
//
//    /**
//     * 批量获取库存状态
//     */
//    @Override
//    public Map<String, Boolean> batchGetStockStatus(List<String> stockKeys) {
//        Map<String, Boolean> result = new HashMap<>();
//
//        if (CollUtil.isEmpty(stockKeys)) {
//            log.warn("批量获取库存状态参数为空");
//            return result;
//        }
//
//        try {
//            List<String> cacheKeys = stockKeys.stream()
//                .filter(StrUtil::isNotBlank)
//                .map(stockKey -> STOCK_STATUS_KEY_PREFIX + stockKey)
//                .collect(java.util.stream.Collectors.toList());
//
//            if (CollUtil.isEmpty(cacheKeys)) {
//                return result;
//            }
//
//            Map<String, Object> cacheMap = RedisUtils.getCacheMap(cacheKeys);
//
//            for (int i = 0; i < stockKeys.size(); i++) {
//                String stockKey = stockKeys.get(i);
//                String cacheKey = STOCK_STATUS_KEY_PREFIX + stockKey;
//                Object value = cacheMap.get(cacheKey);
//
//                if (value != null) {
//                    boolean hasStock = "1".equals(value.toString());
//                    result.put(stockKey, hasStock);
//                } else {
//                    result.put(stockKey, null);
//                }
//            }
//
//            log.debug("批量获取库存状态成功: count={}", result.size());
//
//        } catch (Exception e) {
//            log.error("批量获取库存状态异常: stockKeys={}", stockKeys, e);
//        }
//
//        return result;
//    }
//
//    /**
//     * 删除库存状态缓存
//     */
//    @Override
//    public void deleteStockStatus(String productSkuCode, String warehouseSystemCode) {
//        if (StrUtil.isBlank(productSkuCode) || StrUtil.isBlank(warehouseSystemCode)) {
//            log.warn("删除库存状态参数为空: productSkuCode={}, warehouseSystemCode={}", productSkuCode, warehouseSystemCode);
//            return;
//        }
//
//        try {
//            String key = getStockStatusKey(productSkuCode, warehouseSystemCode);
//            RedisUtils.deleteObject(key);
//            log.debug("删除库存状态成功: key={}", key);
//        } catch (Exception e) {
//            log.error("删除库存状态异常: productSkuCode={}, warehouseSystemCode={}",
//                     productSkuCode, warehouseSystemCode, e);
//        }
//    }
//
//    /**
//     * 检查库存状态是否已初始化
//     */
//    @Override
//    public boolean isStockStatusInitialized(String productSkuCode, String warehouseSystemCode) {
//        if (StrUtil.isBlank(productSkuCode) || StrUtil.isBlank(warehouseSystemCode)) {
//            return false;
//        }
//
//        try {
//            String key = getStockStatusKey(productSkuCode, warehouseSystemCode);
//            return RedisUtils.hasKey(key);
//        } catch (Exception e) {
//            log.error("检查库存状态初始化异常: productSkuCode={}, warehouseSystemCode={}",
//                     productSkuCode, warehouseSystemCode, e);
//            return false;
//        }
//    }
//
//    /**
//     * 清空所有库存状态缓存
//     */
//    @Override
//    public void clearAllStockStatus() {
//        try {
//            Set<String> keys = RedisUtils.keys(STOCK_STATUS_KEY_PREFIX + "*");
//            if (CollUtil.isNotEmpty(keys)) {
//                RedisUtils.deleteObject(keys);
//                log.info("清空所有库存状态缓存成功: count={}", keys.size());
//            }
//        } catch (Exception e) {
//            log.error("清空所有库存状态缓存异常", e);
//        }
//    }
//}
