package com.zsmall.product.entity.iservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.WarningMessage;
import com.zsmall.product.entity.domain.bo.warningMessage.WarningMessageBo;
import com.zsmall.product.entity.domain.vo.warningMessage.WarningMessageVo;

import java.util.Collection;
import java.util.List;

/**
 * 预警消息Service接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface IWarningMessageService extends IService<WarningMessage> {

    /**
     * 查询预警消息
     */
    WarningMessageVo queryById(Long id);

    /**
     * 查询预警消息列表
     */
    TableDataInfo<WarningMessageVo> queryPageList(WarningMessageBo bo, PageQuery pageQuery);

    /**
     * 查询预警消息列表
     */
    List<WarningMessageVo> queryList(WarningMessageBo bo);

    /**
     * 新增预警消息
     */
    Boolean insertByBo(WarningMessageBo bo);

    /**
     * 修改预警消息
     */
    Boolean updateByBo(WarningMessageBo bo);

    /**
     * 校验并批量删除预警消息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据租户类型查询未读消息数量
     */
    Long countUnreadMessages(String tenantType, Integer businessType);

    /**
     * 批量标记消息为已读
     */
    Boolean batchMarkAsRead(List<Long> ids);

    /**
     * 根据租户类型查询消息列表
     */
    List<WarningMessageVo> getMessagesByTenantType(String tenantType);

    /**
     * 创建库存预警消息
     */
    Boolean createStockWarningMessage(String title, String content, String tenantType, Integer businessType);

}
