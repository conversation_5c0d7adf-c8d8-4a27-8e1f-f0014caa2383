<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.WarningMessageMapper">

    <resultMap type="com.zsmall.product.entity.domain.WarningMessage" id="WarningMessageResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="tenantType"    column="tenant_type"    />
        <result property="businessType"    column="business_type"    />
        <result property="content"    column="content"    />
        <result property="isRead"    column="is_read"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWarningMessageVo">
        select id, title, tenant_type, business_type, content, is_read, del_flag, create_by, create_time, update_by, update_time from warning_message
    </sql>

    <!-- 根据租户类型和业务类型查询未读消息数量 -->
    <select id="countUnreadMessages" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM warning_message
        WHERE del_flag = '0'
        AND is_read = 0
        <if test="tenantType != null and tenantType != ''">
            AND tenant_type = #{tenantType}
        </if>
        <if test="businessType != null">
            AND business_type = #{businessType}
        </if>
    </select>

    <!-- 批量标记消息为已读 -->
    <update id="batchMarkAsRead">
        UPDATE warning_message
        SET is_read = 1, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = '0'
    </update>

    <!-- 根据租户类型查询消息列表 -->
    <select id="selectByTenantType" resultType="com.zsmall.product.entity.domain.vo.warningMessage.WarningMessageVo">
        <include refid="selectWarningMessageVo"/>
        WHERE del_flag = '0'
        <if test="tenantType != null and tenantType != ''">
            AND tenant_type = #{tenantType}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>
